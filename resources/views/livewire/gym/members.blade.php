<div>
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold" style="color: var(--text-primary);">Members</h1>
            <p class="text-sm mt-1" style="color: var(--text-muted);">Manage gym members and their information</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <button wire:click="openCreateModal"
                    class="inline-flex items-center px-4 py-2 bg-gym-primary hover:bg-gym-primary/80 text-white text-sm font-medium rounded-lg transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Add New Member
            </button>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="rounded-lg p-4 mb-6" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div class="md:col-span-2">
                <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Search Members</label>
                <input type="text"
                       wire:model.live.debounce.300ms="search"
                       placeholder="Search by name, email, or phone..."
                       class="w-full px-3 py-2 text-sm rounded-md border transition-colors"
                       style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
            </div>

            <!-- Gender Filter -->
            <div>
                <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Gender</label>
                <select wire:model.live="filterGender"
                        class="w-full px-3 py-2 text-sm rounded-md border transition-colors"
                        style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                    <option value="">All Genders</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                </select>
            </div>

            <!-- Per Page -->
            <div>
                <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Per Page</label>
                <select wire:model.live="perPage"
                        class="w-full px-3 py-2 text-sm rounded-md border transition-colors"
                        style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Members Table -->
    <div class="rounded-lg overflow-hidden" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead style="background-color: var(--hover-bg);">
                    <tr>
                        <th class="px-4 py-3 text-left">
                            <button wire:click="sortBy('first_name')" class="flex items-center space-x-1 text-sm font-medium hover:text-gym-primary transition-colors" style="color: var(--text-secondary);">
                                <span>Name</span>
                                @if($sortField === 'first_name')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-xs"></i>
                                @else
                                    <i class="fas fa-sort text-xs opacity-50"></i>
                                @endif
                            </button>
                        </th>
                        <th class="px-4 py-3 text-left">
                            <button wire:click="sortBy('email')" class="flex items-center space-x-1 text-sm font-medium hover:text-gym-primary transition-colors" style="color: var(--text-secondary);">
                                <span>Email</span>
                                @if($sortField === 'email')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-xs"></i>
                                @else
                                    <i class="fas fa-sort text-xs opacity-50"></i>
                                @endif
                            </button>
                        </th>
                        <th class="px-4 py-3 text-left">
                            <span class="text-sm font-medium" style="color: var(--text-secondary);">Phone</span>
                        </th>
                        <th class="px-4 py-3 text-left">
                            <span class="text-sm font-medium" style="color: var(--text-secondary);">Gender</span>
                        </th>
                        <th class="px-4 py-3 text-left">
                            <button wire:click="sortBy('created_at')" class="flex items-center space-x-1 text-sm font-medium hover:text-gym-primary transition-colors" style="color: var(--text-secondary);">
                                <span>Joined</span>
                                @if($sortField === 'created_at')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-xs"></i>
                                @else
                                    <i class="fas fa-sort text-xs opacity-50"></i>
                                @endif
                            </button>
                        </th>
                        <th class="px-4 py-3 text-right">
                            <span class="text-sm font-medium" style="color: var(--text-secondary);">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y" style="border-color: var(--border-primary);">
                    @forelse($members as $member)
                        <tr class="hover:bg-opacity-50 transition-colors" style="background-color: transparent;">
                            <td class="px-4 py-3">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gym-primary rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-medium">
                                            {{ strtoupper(substr($member->first_name, 0, 1)) }}
                                        </span>
                                    </div>
                                    <div>
                                        <p class="font-medium text-sm" style="color: var(--text-primary);">{{ $member->full_name }}</p>
                                        @if($member->date_of_birth)
                                            <p class="text-xs" style="color: var(--text-muted);">
                                                Age: {{ $member->date_of_birth->age }}
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <p class="text-sm" style="color: var(--text-primary);">{{ $member->email ?: 'N/A' }}</p>
                            </td>
                            <td class="px-4 py-3">
                                <p class="text-sm" style="color: var(--text-primary);">{{ $member->phone ?: 'N/A' }}</p>
                            </td>
                            <td class="px-4 py-3">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $member->gender === 'male' ? 'bg-blue-100 text-blue-800' :
                                       ($member->gender === 'female' ? 'bg-pink-100 text-pink-800' : 'bg-gray-100 text-gray-800') }}">
                                    {{ ucfirst($member->gender ?: 'N/A') }}
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <p class="text-sm" style="color: var(--text-primary);">{{ $member->created_at->format('M d, Y') }}</p>
                                <p class="text-xs" style="color: var(--text-muted);">{{ $member->created_at->diffForHumans() }}</p>
                            </td>
                            <td class="px-4 py-3 text-right">
                                <div class="flex items-center justify-end space-x-1">
                                    <button wire:click="checkInMember({{ $member->id }})"
                                            class="p-2 rounded-md transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:ring-opacity-50"
                                            style="color: var(--gym-primary); background-color: transparent;"
                                            onmouseover="this.style.backgroundColor='var(--hover-bg)'"
                                            onmouseout="this.style.backgroundColor='transparent'"
                                            title="Quick Check-in/out">
                                        <i class="fas fa-sign-in-alt text-sm"></i>
                                    </button>
                                    <button wire:click="openViewModal({{ $member->id }})"
                                            class="p-2 rounded-md transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gym-accent focus:ring-opacity-50"
                                            style="color: var(--gym-accent); background-color: transparent;"
                                            onmouseover="this.style.backgroundColor='var(--hover-bg)'"
                                            onmouseout="this.style.backgroundColor='transparent'"
                                            title="View Details">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                    <button wire:click="openEditModal({{ $member->id }})"
                                            class="p-2 rounded-md transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gym-secondary focus:ring-opacity-50"
                                            style="color: var(--gym-secondary); background-color: transparent;"
                                            onmouseover="this.style.backgroundColor='var(--hover-bg)'"
                                            onmouseout="this.style.backgroundColor='transparent'"
                                            title="Edit Member">
                                        <i class="fas fa-edit text-sm"></i>
                                    </button>
                                    <button wire:click="openDeleteModal({{ $member->id }})"
                                            class="p-2 rounded-md transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                                            style="color: #ef4444; background-color: transparent;"
                                            onmouseover="this.style.backgroundColor='var(--hover-bg)'; this.style.color='#dc2626'"
                                            onmouseout="this.style.backgroundColor='transparent'; this.style.color='#ef4444'"
                                            title="Delete Member">
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center">
                                <div class="flex flex-col items-center space-y-2">
                                    <i class="fas fa-users text-4xl" style="color: var(--text-muted);"></i>
                                    <p class="text-lg font-medium" style="color: var(--text-secondary);">No members found</p>
                                    <p class="text-sm" style="color: var(--text-muted);">
                                        @if($search)
                                            Try adjusting your search criteria
                                        @else
                                            Get started by adding your first member
                                        @endif
                                    </p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($members->hasPages())
            <div class="px-4 py-3 border-t" style="border-color: var(--border-primary);">
                {{ $members->links() }}
            </div>
        @endif
    </div>

    <!-- Create/Edit Modal -->
    @if($showCreateModal || $showEditModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
             wire:click.self="closeModals">
            <div class="rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                 style="background-color: var(--card-bg);">
                <div class="flex items-center justify-between p-6 border-b" style="border-color: var(--border-primary);">
                    <h3 class="text-lg font-semibold" style="color: var(--text-primary);">
                        {{ $showCreateModal ? 'Add New Member' : 'Edit Member' }}
                    </h3>
                    <button wire:click="closeModals"
                            class="p-2 rounded-md transition-colors hover:bg-opacity-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
                            style="color: var(--text-muted); background-color: transparent;"
                            onmouseover="this.style.backgroundColor='var(--hover-bg)'"
                            onmouseout="this.style.backgroundColor='transparent'">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form wire:submit="save" class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Main Information -->
                        <div class="lg:col-span-2">
                            <h4 class="text-md font-semibold mb-4" style="color: var(--text-primary);">Personal Information</h4>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- First Name -->
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">
                                        First Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           wire:model="first_name"
                                           class="w-full px-3 py-2 text-sm rounded-md border transition-colors focus:outline-none focus:ring-2 focus:ring-gym-primary focus:ring-opacity-50 @error('first_name') border-red-500 focus:ring-red-500 @enderror"
                                           style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                           placeholder="Enter first name">
                                    @error('first_name')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Last Name -->
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Last Name</label>
                                    <input type="text"
                                           wire:model="last_name"
                                           class="w-full px-3 py-2 text-sm rounded-md border transition-colors focus:outline-none focus:ring-2 focus:ring-gym-primary focus:ring-opacity-50 @error('last_name') border-red-500 focus:ring-red-500 @enderror"
                                           style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                           placeholder="Enter last name">
                                    @error('last_name')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Email -->
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Email</label>
                                    <input type="email"
                                           wire:model="email"
                                           class="w-full px-3 py-2 text-sm rounded-md border transition-colors focus:outline-none focus:ring-2 focus:ring-gym-primary focus:ring-opacity-50 @error('email') border-red-500 @enderror"
                                           style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                           placeholder="Enter email address">
                                    @error('email')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Phone -->
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Phone</label>
                                    <input type="tel"
                                           wire:model="phone"
                                           id="phone-input"
                                           class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('phone') border-red-500 @enderror"
                                           style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                           placeholder="(*************">
                                    @error('phone')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Date of Birth -->
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Date of Birth</label>
                                    <input type="date"
                                           wire:model="date_of_birth"
                                           class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('date_of_birth') border-red-500 @enderror"
                                           style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                                    @error('date_of_birth')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Gender -->
                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Gender</label>
                                    <select wire:model="gender"
                                            class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('gender') border-red-500 @enderror"
                                            style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                                        <option value="">Select Gender</option>
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                        <option value="other">Other</option>
                                    </select>
                                    @error('gender')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Address Section -->
                            <div class="mt-6">
                                <h5 class="text-sm font-medium mb-3" style="color: var(--text-primary);">Address Information</h5>

                                <div class="grid grid-cols-1 gap-4">
                                    <!-- Address -->
                                    <div>
                                        <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Street Address</label>
                                        <input type="text"
                                               wire:model="address"
                                               class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('address') border-red-500 @enderror"
                                               style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                               placeholder="Enter street address">
                                        @error('address')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                        <!-- City -->
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">City</label>
                                            <input type="text"
                                                   wire:model="city"
                                                   class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('city') border-red-500 @enderror"
                                                   style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                                   placeholder="Enter city">
                                            @error('city')
                                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- State -->
                                        <div>
                                            <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">State</label>
                                            <input type="text"
                                                   wire:model="state"
                                                   class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('state') border-red-500 @enderror"
                                                   style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                                   placeholder="State">
                                            @error('state')
                                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- ZIP -->
                                        <div>
                                            <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">ZIP Code</label>
                                            <input type="text"
                                                   wire:model="zip"
                                                   id="zip-input"
                                                   class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('zip') border-red-500 @enderror"
                                                   style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                                   placeholder="12345">
                                            @error('zip')
                                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Country -->
                                    <div>
                                        <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Country</label>
                                        <input type="text"
                                               wire:model="country"
                                               class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('country') border-red-500 @enderror"
                                               style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                               placeholder="Enter country">
                                        @error('country')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar Information -->
                        <div class="lg:col-span-1 space-y-6">
                            <!-- Emergency Contact -->
                            <div>
                                <h5 class="text-sm font-medium mb-3" style="color: var(--text-primary);">Emergency Contact</h5>

                                <div class="space-y-4">
                                    <!-- Emergency Contact Name -->
                                    <div>
                                        <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Contact Name</label>
                                        <input type="text"
                                               wire:model="emergency_contact_name"
                                               class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('emergency_contact_name') border-red-500 @enderror"
                                               style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                               placeholder="Enter contact name">
                                        @error('emergency_contact_name')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Emergency Contact Phone -->
                                    <div>
                                        <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Contact Phone</label>
                                        <input type="tel"
                                               wire:model="emergency_contact_phone"
                                               id="emergency-phone-input"
                                               class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('emergency_contact_phone') border-red-500 @enderror"
                                               style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                               placeholder="(*************">
                                        @error('emergency_contact_phone')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Emergency Contact Relationship -->
                                    <div>
                                        <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Relationship</label>
                                        <select wire:model="emergency_contact_relationship"
                                                class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('emergency_contact_relationship') border-red-500 @enderror"
                                                style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                                            <option value="">Select Relationship</option>
                                            <option value="spouse">Spouse</option>
                                            <option value="parent">Parent</option>
                                            <option value="child">Child</option>
                                            <option value="sibling">Sibling</option>
                                            <option value="friend">Friend</option>
                                            <option value="other">Other</option>
                                        </select>
                                        @error('emergency_contact_relationship')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Photo Upload -->
                            <div>
                                <h5 class="text-sm font-medium mb-3" style="color: var(--text-primary);">Profile Photo</h5>

                                <div class="space-y-4">
                                    @if ($photo)
                                        <div class="text-center">
                                            <img src="{{ $photo->temporaryUrl() }}" class="w-24 h-24 rounded-full mx-auto object-cover">
                                            <p class="text-xs mt-2" style="color: var(--text-muted);">Preview</p>
                                        </div>
                                    @elseif ($showEditModal && $memberId)
                                        @php
                                            $currentMember = \App\Models\Gym\Member::find($memberId);
                                        @endphp
                                        @if($currentMember && $currentMember->photo)
                                            <div class="text-center">
                                                <img src="{{ asset('storage/' . $currentMember->photo) }}" class="w-24 h-24 rounded-full mx-auto object-cover">
                                                <p class="text-xs mt-2" style="color: var(--text-muted);">Current Photo</p>
                                            </div>
                                        @endif
                                    @endif

                                    <div>
                                        <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Upload Photo</label>
                                        <input type="file"
                                               wire:model="photo"
                                               accept="image/*"
                                               class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('photo') border-red-500 @enderror"
                                               style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);">
                                        @error('photo')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                        <p class="text-xs mt-1" style="color: var(--text-muted);">Max file size: 2MB</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div>
                                <h5 class="text-sm font-medium mb-3" style="color: var(--text-primary);">Notes</h5>

                                <div>
                                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">Additional Notes</label>
                                    <textarea wire:model="notes"
                                              rows="4"
                                              class="w-full px-3 py-2 text-sm rounded-md border transition-colors @error('notes') border-red-500 @enderror"
                                              style="background-color: var(--input-bg); border-color: var(--border-secondary); color: var(--text-primary);"
                                              placeholder="Enter any additional notes about the member..."></textarea>
                                    @error('notes')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex items-center justify-between mt-8 pt-6 border-t">
                        <div class="flex items-center space-x-4">
                            <button type="submit"
                                    class="inline-flex items-center px-6 py-2 bg-gym-primary hover:bg-gym-primary/80 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-save mr-2"></i>
                                {{ $showCreateModal ? 'Create Member' : 'Update Member' }}
                            </button>

                            <button type="button"
                                    wire:click="closeModals"
                                    class="inline-flex items-center px-6 py-2 border text-sm font-medium rounded-lg transition-colors hover:bg-opacity-50"
                                    style="border-color: var(--border-secondary); color: var(--text-secondary);">
                                Cancel
                            </button>
                        </div>

                        @if($showEditModal && $memberId)
                            <div>
                                <p class="text-xs" style="color: var(--text-muted);">
                                    Member ID: {{ $memberId }}
                                </p>
                            </div>
                        @endif
                    </div>
                </form>
            </div>
        </div>
    @endif

    <!-- View Modal -->
    @if($showViewModal && $viewMember)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
             wire:click.self="closeModals">
            <div class="rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto"
                 style="background-color: var(--card-bg);">
                <div class="flex items-center justify-between p-6 border-b" style="border-color: var(--border-primary);">
                    <div class="flex items-center space-x-4">
                        @if($viewMember->photo)
                            <img src="{{ asset('storage/' . $viewMember->photo) }}" class="w-12 h-12 rounded-full object-cover">
                        @else
                            <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                <span class="text-white text-lg font-bold">
                                    {{ strtoupper(substr($viewMember->first_name, 0, 1)) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <h3 class="text-lg font-semibold" style="color: var(--text-primary);">{{ $viewMember->full_name }}</h3>
                            <p class="text-sm" style="color: var(--text-muted);">Member since {{ $viewMember->created_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button wire:click="checkInMember({{ $viewMember->id }})"
                                class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-50"
                                style="background-color: var(--gym-primary); color: white; border: none;"
                                onmouseover="this.style.opacity='0.8'"
                                onmouseout="this.style.opacity='1'">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Quick Check-in
                        </button>
                        <button wire:click="closeModals"
                                class="p-2 rounded-md transition-colors hover:bg-opacity-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
                                style="color: var(--text-muted); background-color: transparent;"
                                onmouseover="this.style.backgroundColor='var(--hover-bg)'"
                                onmouseout="this.style.backgroundColor='transparent'">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <div class="p-6">
                    <!-- Tab Navigation -->
                    <div class="border-b mb-6" style="border-color: var(--border-primary);">
                        <nav class="flex space-x-8">
                            <button wire:click="setActiveTab('overview')"
                                    class="py-2 px-1 border-b-2 font-medium text-sm transition-colors focus:outline-none"
                                    style="border-color: {{ $activeTab === 'overview' ? 'var(--gym-primary)' : 'transparent' }}; color: {{ $activeTab === 'overview' ? 'var(--gym-primary)' : 'var(--text-secondary)' }};">
                                Overview
                            </button>
                            <button wire:click="setActiveTab('packages')"
                                    class="py-2 px-1 border-b-2 font-medium text-sm transition-colors focus:outline-none"
                                    style="border-color: {{ $activeTab === 'packages' ? 'var(--gym-primary)' : 'transparent' }}; color: {{ $activeTab === 'packages' ? 'var(--gym-primary)' : 'var(--text-secondary)' }};">
                                Packages
                            </button>
                            <button wire:click="setActiveTab('checkins')"
                                    class="py-2 px-1 border-b-2 font-medium text-sm transition-colors focus:outline-none"
                                    style="border-color: {{ $activeTab === 'checkins' ? 'var(--gym-primary)' : 'transparent' }}; color: {{ $activeTab === 'checkins' ? 'var(--gym-primary)' : 'var(--text-secondary)' }};">
                                Check-ins
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    @if($activeTab === 'overview')
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Personal Information -->
                            <div class="lg:col-span-2">
                                <div class="rounded-lg p-6" style="background-color: var(--hover-bg); border: 1px solid var(--border-secondary);">
                                    <h4 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Personal Information</h4>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium" style="color: var(--text-secondary);">Full Name</label>
                                            <p class="mt-1 text-sm" style="color: var(--text-primary);">{{ $viewMember->full_name ?: 'N/A' }}</p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium" style="color: var(--text-secondary);">Email</label>
                                            <p class="mt-1 text-sm" style="color: var(--text-primary);">{{ $viewMember->email ?: 'N/A' }}</p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium" style="color: var(--text-secondary);">Phone</label>
                                            <p class="mt-1 text-sm" style="color: var(--text-primary);">{{ $viewMember->phone ?: 'N/A' }}</p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium" style="color: var(--text-secondary);">Date of Birth</label>
                                            <p class="mt-1 text-sm" style="color: var(--text-primary);">
                                                {{ $viewMember->date_of_birth ? $viewMember->date_of_birth->format('M d, Y') . ' (Age: ' . $viewMember->date_of_birth->age . ')' : 'N/A' }}
                                            </p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium" style="color: var(--text-secondary);">Gender</label>
                                            <p class="mt-1 text-sm" style="color: var(--text-primary);">{{ ucfirst($viewMember->gender) ?: 'N/A' }}</p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium" style="color: var(--text-secondary);">Member Since</label>
                                            <p class="mt-1 text-sm" style="color: var(--text-primary);">{{ $viewMember->created_at->format('M d, Y') }}</p>
                                        </div>
                                    </div>

                                    @if($viewMember->address || $viewMember->city || $viewMember->state || $viewMember->zip || $viewMember->country)
                                        <div class="mt-6">
                                            <h5 class="text-md font-medium mb-3" style="color: var(--text-primary);">Address</h5>
                                            <div class="text-sm" style="color: var(--text-primary);">
                                                @if($viewMember->address)
                                                    <p>{{ $viewMember->address }}</p>
                                                @endif
                                                @if($viewMember->city || $viewMember->state || $viewMember->zip)
                                                    <p>
                                                        {{ $viewMember->city }}{{ $viewMember->city && ($viewMember->state || $viewMember->zip) ? ', ' : '' }}
                                                        {{ $viewMember->state }}{{ $viewMember->state && $viewMember->zip ? ' ' : '' }}
                                                        {{ $viewMember->zip }}
                                                    </p>
                                                @endif
                                                @if($viewMember->country)
                                                    <p>{{ $viewMember->country }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    @endif

                                    @if($viewMember->emergency_contact_name || $viewMember->emergency_contact_phone || $viewMember->emergency_contact_relationship)
                                        <div class="mt-6">
                                            <h5 class="text-md font-medium mb-3" style="color: var(--text-primary);">Emergency Contact</h5>
                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                <div>
                                                    <label class="block text-sm font-medium" style="color: var(--text-secondary);">Name</label>
                                                    <p class="mt-1 text-sm" style="color: var(--text-primary);">{{ $viewMember->emergency_contact_name ?: 'N/A' }}</p>
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium" style="color: var(--text-secondary);">Phone</label>
                                                    <p class="mt-1 text-sm" style="color: var(--text-primary);">{{ $viewMember->emergency_contact_phone ?: 'N/A' }}</p>
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium" style="color: var(--text-secondary);">Relationship</label>
                                                    <p class="mt-1 text-sm" style="color: var(--text-primary);">{{ ucfirst($viewMember->emergency_contact_relationship) ?: 'N/A' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                    @if($viewMember->notes)
                                        <div class="mt-6">
                                            <h5 class="text-md font-medium mb-3" style="color: var(--text-primary);">Notes</h5>
                                            <p class="text-sm" style="color: var(--text-primary);">{{ $viewMember->notes }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Sidebar -->
                            <div class="lg:col-span-1 space-y-6">
                                <!-- Quick Actions -->
                                <div class="rounded-lg p-6" style="background-color: var(--hover-bg); border: 1px solid var(--border-secondary);">
                                    <h4 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">Quick Actions</h4>
                                    <div class="space-y-3">
                                        <button wire:click="openEditModal({{ $viewMember->id }})"
                                                class="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-50"
                                                style="background-color: var(--gym-secondary); color: white; border: none;"
                                                onmouseover="this.style.opacity='0.8'"
                                                onmouseout="this.style.opacity='1'">
                                            <i class="fas fa-edit mr-2"></i>
                                            Edit Member
                                        </button>
                                        <button wire:click="openDeleteModal({{ $viewMember->id }})"
                                                class="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-50"
                                                style="background-color: #ef4444; color: white; border: none;"
                                                onmouseover="this.style.backgroundColor='#dc2626'"
                                                onmouseout="this.style.backgroundColor='#ef4444'">
                                            <i class="fas fa-trash mr-2"></i>
                                            Delete Member
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Delete Confirmation Modal -->
    @if($showDeleteModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
             wire:click.self="closeModals">
            <div class="rounded-lg shadow-xl max-w-md w-full"
                 style="background-color: var(--card-bg);">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold" style="color: var(--text-primary);">Delete Member</h3>
                            <p class="text-sm" style="color: var(--text-muted);">This action cannot be undone</p>
                        </div>
                    </div>

                    <p class="text-sm mb-6" style="color: var(--text-secondary);">
                        Are you sure you want to delete this member? All associated data including check-ins and package history will be permanently removed.
                    </p>

                    <div class="flex items-center justify-end space-x-3">
                        <button wire:click="closeModals"
                                class="px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
                                style="border: 1px solid var(--border-secondary); color: var(--text-secondary); background-color: transparent;"
                                onmouseover="this.style.backgroundColor='var(--hover-bg)'"
                                onmouseout="this.style.backgroundColor='transparent'">
                            Cancel
                        </button>
                        <button wire:click="delete"
                                class="px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                                style="background-color: #ef4444; color: white; border: none;"
                                onmouseover="this.style.backgroundColor='#dc2626'"
                                onmouseout="this.style.backgroundColor='#ef4444'">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Member
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="fixed top-4 right-4 z-50">
            <div class="px-4 py-3 rounded-lg shadow-lg text-white text-sm font-medium"
                 style="background-color: #10b981;">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    {{ session('message') }}
                </div>
            </div>
        </div>
    @endif

    <!-- Loading State -->
    <div wire:loading class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="rounded-lg p-6 flex items-center space-x-3" style="background-color: var(--card-bg);">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2" style="border-color: var(--gym-primary);"></div>
            <span style="color: var(--text-primary);">Processing...</span>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://unpkg.com/imask"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Phone number masking
        const phoneInputs = document.querySelectorAll('#phone-input, #emergency-phone-input');
        phoneInputs.forEach(input => {
            IMask(input, {
                mask: '(*************'
            });
        });

        // ZIP code masking
        const zipInput = document.querySelector('#zip-input');
        if (zipInput) {
            IMask(zipInput, {
                mask: '00000'
            });
        }
    });

    // Auto-hide flash messages
    document.addEventListener('DOMContentLoaded', function() {
        const flashMessage = document.querySelector('.fixed.top-4.right-4');
        if (flashMessage) {
            setTimeout(() => {
                flashMessage.style.opacity = '0';
                setTimeout(() => {
                    flashMessage.remove();
                }, 300);
            }, 3000);
        }
    });
</script>
@endpush