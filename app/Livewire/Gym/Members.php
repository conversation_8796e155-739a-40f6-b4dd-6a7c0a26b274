<?php

namespace App\Livewire\Gym;

use App\Models\Gym\Member;
use App\Models\Gym\CheckIn;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;

class Members extends Component
{
    use WithPagination, WithFileUploads;

    // Modal states
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showViewModal = false;
    public $showDeleteModal = false;

    // List functionality
    public $search = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 10;
    public $filterGender = '';

    // Form fields
    public $memberId;
    public $first_name = '';
    public $last_name = '';
    public $email = '';
    public $phone = '';
    public $date_of_birth = '';
    public $gender = '';
    public $address = '';
    public $city = '';
    public $state = '';
    public $zip = '';
    public $country = '';
    public $emergency_contact_name = '';
    public $emergency_contact_phone = '';
    public $emergency_contact_relationship = '';
    public $photo;
    public $notes = '';

    // View modal data
    public $viewMember;
    public $activeTab = 'overview';
    public $checkInDateFrom = '';
    public $checkInDateTo = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'perPage' => ['except' => 10],
        'filterGender' => ['except' => ''],
    ];

    protected function rules()
    {
        return [
            'first_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'last_name' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'email' => [
                'nullable',
                'email:rfc,dns',
                'max:255',
                $this->memberId
                    ? 'unique:gym_members,email,' . $this->memberId
                    : 'unique:gym_members,email'
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[1-9][\d]{0,15}$/',
            ],
            'date_of_birth' => [
                'nullable',
                'date',
                'before:today',
                'after:1900-01-01',
            ],
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'city' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'state' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'zip' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[a-zA-Z0-9\s\-]+$/',
            ],
            'country' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'emergency_contact_name' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'emergency_contact_phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[1-9][\d]{0,15}$/',
            ],
            'emergency_contact_relationship' => 'nullable|in:spouse,parent,child,sibling,friend,other',
            'photo' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif',
                'max:2048',
                'dimensions:min_width=100,min_height=100,max_width=2000,max_height=2000',
            ],
            'notes' => 'nullable|string|max:1000',
        ];
    }

    protected $validationAttributes = [
        'first_name' => 'first name',
        'last_name' => 'last name',
        'date_of_birth' => 'date of birth',
        'emergency_contact_name' => 'emergency contact name',
        'emergency_contact_phone' => 'emergency contact phone',
        'emergency_contact_relationship' => 'emergency contact relationship',
    ];

    protected $messages = [
        'first_name.required' => 'The first name field is required.',
        'first_name.regex' => 'The first name may only contain letters, spaces, hyphens, apostrophes, and dots.',
        'last_name.regex' => 'The last name may only contain letters, spaces, hyphens, apostrophes, and dots.',
        'email.email' => 'Please enter a valid email address.',
        'email.unique' => 'This email address is already registered.',
        'phone.regex' => 'Please enter a valid phone number.',
        'date_of_birth.before' => 'The date of birth must be before today.',
        'date_of_birth.after' => 'Please enter a valid date of birth.',
        'city.regex' => 'The city name may only contain letters, spaces, hyphens, apostrophes, and dots.',
        'state.regex' => 'The state name may only contain letters, spaces, hyphens, apostrophes, and dots.',
        'zip.regex' => 'Please enter a valid ZIP/postal code.',
        'country.regex' => 'The country name may only contain letters, spaces, hyphens, apostrophes, and dots.',
        'emergency_contact_name.regex' => 'The emergency contact name may only contain letters, spaces, hyphens, apostrophes, and dots.',
        'emergency_contact_phone.regex' => 'Please enter a valid emergency contact phone number.',
        'photo.image' => 'The file must be an image.',
        'photo.mimes' => 'The photo must be a file of type: jpeg, png, jpg, gif.',
        'photo.max' => 'The photo may not be greater than 2MB.',
        'photo.dimensions' => 'The photo must be between 100x100 and 2000x2000 pixels.',
    ];

    // List functionality methods
    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFilterGender()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
    }

    // Modal management methods
    public function openCreateModal()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function openEditModal($memberId)
    {
        $member = Member::findOrFail($memberId);
        $this->memberId = $member->id;
        $this->fill($member->toArray());
        $this->showEditModal = true;
    }

    public function openViewModal($memberId)
    {
        $this->viewMember = Member::with(['memberPackages.package', 'checkIns'])->findOrFail($memberId);
        $this->activeTab = 'overview';
        $this->showViewModal = true;
    }

    public function openDeleteModal($memberId)
    {
        $this->memberId = $memberId;
        $this->showDeleteModal = true;
    }

    public function closeModals()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showViewModal = false;
        $this->showDeleteModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->reset([
            'memberId', 'first_name', 'last_name', 'email', 'phone', 'date_of_birth',
            'gender', 'address', 'city', 'state', 'zip', 'country',
            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',
            'photo', 'notes'
        ]);
        $this->resetErrorBag();
    }

    // CRUD operations
    public function save()
    {
        $this->validate();

        $data = [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'date_of_birth' => $this->date_of_birth,
            'gender' => $this->gender,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'zip' => $this->zip,
            'country' => $this->country,
            'emergency_contact_name' => $this->emergency_contact_name,
            'emergency_contact_phone' => $this->emergency_contact_phone,
            'emergency_contact_relationship' => $this->emergency_contact_relationship,
            'notes' => $this->notes,
        ];

        // Handle photo upload
        if ($this->photo) {
            $data['photo'] = $this->photo->store('member-photos', 'public');
        }

        if ($this->memberId) {
            // Update existing member
            $data['updated_by'] = auth()->id();
            $member = Member::findOrFail($this->memberId);
            $member->update($data);
            $message = 'Member updated successfully.';
        } else {
            // Create new member
            $data['created_by'] = auth()->id();
            Member::create($data);
            $message = 'Member created successfully.';
        }

        $this->closeModals();
        session()->flash('message', $message);
    }

    public function delete()
    {
        $member = Member::findOrFail($this->memberId);
        $member->delete();

        $this->closeModals();
        session()->flash('message', 'Member deleted successfully.');
    }

    // View modal methods
    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function checkInMember($memberId)
    {
        $existingCheckIn = CheckIn::where('member_id', $memberId)
            ->whereNull('check_out_time')
            ->first();

        if ($existingCheckIn) {
            // Check out
            $existingCheckIn->update([
                'check_out_time' => now(),
                'updated_by' => auth()->id(),
            ]);
            $message = 'Member checked out successfully.';
        } else {
            // Check in
            CheckIn::create([
                'member_id' => $memberId,
                'check_in_time' => now(),
                'created_by' => auth()->id(),
            ]);
            $message = 'Member checked in successfully.';
        }

        // Refresh view member data if modal is open
        if ($this->showViewModal && $this->viewMember->id == $memberId) {
            $this->viewMember = Member::with(['memberPackages.package', 'checkIns'])->findOrFail($memberId);
        }

        session()->flash('message', $message);
    }

    public function render()
    {
        $members = Member::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('phone', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->filterGender, function ($query) {
                $query->where('gender', $this->filterGender);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        return view('livewire.gym.members', [
            'members' => $members,
        ]);
    }
}