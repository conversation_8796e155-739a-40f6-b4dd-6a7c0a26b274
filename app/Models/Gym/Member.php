<?php

namespace App\Models\Gym;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Member extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'gym_members';

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'date_of_birth',
        'gender',
        'address',
        'city',
        'state',
        'zip',
        'country',
        'emergency_contact_name',
        'emergency_contact_phone',
        'emergency_contact_relationship',
        'photo',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
    ];

    // Relationships
    public function packages()
    {
        return $this->belongsToMany(Package::class, 'gym_member_packages')
                    ->withPivot(['start_date', 'end_date', 'status'])
                    ->withTimestamps();
    }

    public function memberPackages()
    {
        return $this->hasMany(MemberPackage::class);
    }

    public function checkIns()
    {
        return $this->hasMany(CheckIn::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    // Helper methods
    public function getFullNameAttribute()
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getActivePackagesAttribute()
    {
        return $this->memberPackages()->where('status', 'active')->get();
    }
}
