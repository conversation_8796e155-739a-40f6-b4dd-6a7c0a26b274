<div>
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
        <div class="stats-card rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs font-medium" style="color: var(--text-muted);">Total Members</p>
                    <p class="text-2xl font-bold mt-0.5" style="color: var(--text-primary);">1,247</p>
                    <p class="text-gym-primary text-xs mt-0.5">
                        <i class="fas fa-arrow-up mr-1"></i>+12% from last month
                    </p>
                </div>
                <div class="w-10 h-10 bg-gym-primary/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-gym-primary text-lg"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs font-medium" style="color: var(--text-muted);">Active Today</p>
                    <p class="text-2xl font-bold mt-0.5" style="color: var(--text-primary);">89</p>
                    <p class="text-gym-secondary text-xs mt-0.5">
                        <i class="fas fa-arrow-up mr-1"></i>+5% from yesterday
                    </p>
                </div>
                <div class="w-10 h-10 bg-gym-secondary/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-sign-in-alt text-gym-secondary text-lg"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs font-medium" style="color: var(--text-muted);">Revenue</p>
                    <p class="text-2xl font-bold mt-0.5" style="color: var(--text-primary);">$24,580</p>
                    <p class="text-gym-accent text-xs mt-0.5">
                        <i class="fas fa-arrow-up mr-1"></i>+8% from last month
                    </p>
                </div>
                <div class="w-10 h-10 bg-gym-accent/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-gym-accent text-lg"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs font-medium" style="color: var(--text-muted);">Classes Today</p>
                    <p class="text-2xl font-bold mt-0.5" style="color: var(--text-primary);">12</p>
                    <p class="text-gym-blue text-xs mt-0.5">
                        <i class="fas fa-check mr-1"></i>All scheduled
                    </p>
                </div>
                <div class="w-10 h-10 bg-gym-blue/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-check text-gym-blue text-lg"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
        <!-- Quick Actions -->
        <div class="lg:col-span-1">
            <div class="rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                <h3 class="text-base font-semibold mb-2" style="color: var(--text-primary);">Quick Actions</h3>
                <div class="space-y-2">
                    <button class="quick-action-btn w-full flex items-center justify-between p-2 bg-gym-primary/10 hover:bg-gym-primary/20 rounded-md transition-all duration-300 group">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-user-plus text-gym-primary text-sm"></i>
                            <span class="text-sm" style="color: var(--text-primary);">Add New Member</span>
                        </div>
                        <i class="fas fa-arrow-right group-hover:text-gym-primary transition-colors text-xs" style="color: var(--text-muted);"></i>
                    </button>

                    <button class="quick-action-btn w-full flex items-center justify-between p-2 bg-gym-secondary/10 hover:bg-gym-secondary/20 rounded-md transition-all duration-300 group">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-sign-in-alt text-gym-secondary text-sm"></i>
                            <span class="text-sm" style="color: var(--text-primary);">Quick Check-in</span>
                        </div>
                        <i class="fas fa-arrow-right group-hover:text-gym-secondary transition-colors text-xs" style="color: var(--text-muted);"></i>
                    </button>

                    <button class="quick-action-btn w-full flex items-center justify-between p-2 bg-gym-accent/10 hover:bg-gym-accent/20 rounded-md transition-all duration-300 group">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-calendar-plus text-gym-accent text-sm"></i>
                            <span class="text-sm" style="color: var(--text-primary);">Schedule Class</span>
                        </div>
                        <i class="fas fa-arrow-right group-hover:text-gym-accent transition-colors text-xs" style="color: var(--text-muted);"></i>
                    </button>

                    <button class="quick-action-btn w-full flex items-center justify-between p-2 bg-gym-blue/10 hover:bg-gym-blue/20 rounded-md transition-all duration-300 group">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-chart-line text-gym-blue text-sm"></i>
                            <span class="text-sm" style="color: var(--text-primary);">View Reports</span>
                        </div>
                        <i class="fas fa-arrow-right group-hover:text-gym-blue transition-colors text-xs" style="color: var(--text-muted);"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="lg:col-span-2">
            <div class="rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-base font-semibold" style="color: var(--text-primary);">Recent Activity</h3>
                    <button class="text-gym-primary hover:text-gym-primary/80 text-xs font-medium">View All</button>
                </div>
                <div class="space-y-2">
                    <div class="activity-card flex items-center space-x-3 p-2 rounded-md" style="background-color: var(--hover-bg);">
                        <div class="w-8 h-8 bg-gym-primary rounded-full flex items-center justify-center">
                            <i class="fas fa-sign-in-alt text-white text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-sm" style="color: var(--text-primary);">John Smith checked in</p>
                            <p class="text-xs" style="color: var(--text-muted);">Premium member • 2 minutes ago</p>
                        </div>
                        <span class="text-gym-primary text-xs font-medium">Active</span>
                    </div>

                    <div class="activity-card flex items-center space-x-3 p-2 rounded-md" style="background-color: var(--hover-bg);">
                        <div class="w-8 h-8 bg-gym-secondary rounded-full flex items-center justify-center">
                            <i class="fas fa-user-plus text-white text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-sm" style="color: var(--text-primary);">Sarah Johnson registered</p>
                            <p class="text-xs" style="color: var(--text-muted);">Basic membership • 15 minutes ago</p>
                        </div>
                        <span class="text-gym-secondary text-xs font-medium">New</span>
                    </div>

                    <div class="activity-card flex items-center space-x-3 p-2 rounded-md" style="background-color: var(--hover-bg);">
                        <div class="w-8 h-8 bg-gym-accent rounded-full flex items-center justify-center">
                            <i class="fas fa-credit-card text-white text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-sm" style="color: var(--text-primary);">Payment received from Mike Davis</p>
                            <p class="text-xs" style="color: var(--text-muted);">$89.99 • 1 hour ago</p>
                        </div>
                        <span class="text-gym-accent text-xs font-medium">Paid</span>
                    </div>

                    <div class="activity-card flex items-center space-x-3 p-2 rounded-md" style="background-color: var(--hover-bg);">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-dumbbell text-white text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-sm" style="color: var(--text-primary);">HIIT class completed</p>
                            <p class="text-xs" style="color: var(--text-muted);">18 attendees • 2 hours ago</p>
                        </div>
                        <span class="text-green-400 text-xs font-medium">Complete</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>